<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bouncing Ball in Spinning Hexagon</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }
        
        canvas {
            border: 2px solid #fff;
            border-radius: 10px;
            box-shadow: 0 0 30px rgba(255, 255, 255, 0.3);
        }
        
        .controls {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            z-index: 100;
        }
        
        .controls button {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .controls button:hover {
            background: rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="controls">
        <button onclick="togglePause()">Pause/Resume</button>
        <button onclick="resetBall()">Reset Ball</button>
        <button onclick="changeSpeed()">Change Speed</button>
    </div>
    
    <canvas id="gameCanvas" width="800" height="600"></canvas>
    
    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        
        // Game state
        let isPaused = false;
        let speedMultiplier = 1;
        
        // 3D transformation utilities
        class Vector3 {
            constructor(x = 0, y = 0, z = 0) {
                this.x = x;
                this.y = y;
                this.z = z;
            }
            
            add(v) {
                return new Vector3(this.x + v.x, this.y + v.y, this.z + v.z);
            }
            
            multiply(scalar) {
                return new Vector3(this.x * scalar, this.y * scalar, this.z * scalar);
            }
            
            length() {
                return Math.sqrt(this.x * this.x + this.y * this.y + this.z * this.z);
            }
            
            normalize() {
                const len = this.length();
                return len > 0 ? new Vector3(this.x / len, this.y / len, this.z / len) : new Vector3();
            }
        }
        
        // 3D to 2D projection
        function project3D(point, distance = 400) {
            const scale = distance / (distance + point.z);
            return {
                x: canvas.width / 2 + point.x * scale,
                y: canvas.height / 2 + point.y * scale,
                scale: scale
            };
        }
        
        // Hexagon class
        class Hexagon {
            constructor(radius = 200) {
                this.radius = radius;
                this.rotation = 0;
                this.rotationSpeed = 0.02;
                this.vertices = [];
                this.generateVertices();
            }
            
            generateVertices() {
                this.vertices = [];
                for (let i = 0; i < 6; i++) {
                    const angle = (i * Math.PI) / 3;
                    this.vertices.push(new Vector3(
                        Math.cos(angle) * this.radius,
                        Math.sin(angle) * this.radius,
                        0
                    ));
                }
            }
            
            update() {
                this.rotation += this.rotationSpeed * speedMultiplier;
            }
            
            getRotatedVertices() {
                return this.vertices.map(vertex => {
                    const cos = Math.cos(this.rotation);
                    const sin = Math.sin(this.rotation);
                    return new Vector3(
                        vertex.x * cos - vertex.y * sin,
                        vertex.x * sin + vertex.y * cos,
                        vertex.z
                    );
                });
            }
            
            draw() {
                const rotatedVertices = this.getRotatedVertices();
                const projectedVertices = rotatedVertices.map(v => project3D(v));
                
                ctx.beginPath();
                ctx.strokeStyle = '#00ffff';
                ctx.lineWidth = 3;
                ctx.shadowColor = '#00ffff';
                ctx.shadowBlur = 10;
                
                projectedVertices.forEach((vertex, i) => {
                    if (i === 0) {
                        ctx.moveTo(vertex.x, vertex.y);
                    } else {
                        ctx.lineTo(vertex.x, vertex.y);
                    }
                });
                ctx.closePath();
                ctx.stroke();
                
                ctx.shadowBlur = 0;
            }
            
            // Check if point is inside hexagon (2D collision detection)
            isPointInside(point) {
                const rotatedVertices = this.getRotatedVertices();
                const projectedVertices = rotatedVertices.map(v => project3D(v));
                
                let inside = false;
                for (let i = 0, j = projectedVertices.length - 1; i < projectedVertices.length; j = i++) {
                    if (((projectedVertices[i].y > point.y) !== (projectedVertices[j].y > point.y)) &&
                        (point.x < (projectedVertices[j].x - projectedVertices[i].x) * (point.y - projectedVertices[i].y) / 
                         (projectedVertices[j].y - projectedVertices[i].y) + projectedVertices[i].x)) {
                        inside = !inside;
                    }
                }
                return inside;
            }
            
            // Get collision normal for bouncing
            getCollisionNormal(point) {
                const rotatedVertices = this.getRotatedVertices();
                const projectedVertices = rotatedVertices.map(v => project3D(v));
                
                let closestDistance = Infinity;
                let normal = new Vector3(0, -1, 0);
                
                for (let i = 0; i < projectedVertices.length; i++) {
                    const j = (i + 1) % projectedVertices.length;
                    const edge = {
                        x: projectedVertices[j].x - projectedVertices[i].x,
                        y: projectedVertices[j].y - projectedVertices[i].y
                    };
                    
                    const toPoint = {
                        x: point.x - projectedVertices[i].x,
                        y: point.y - projectedVertices[i].y
                    };
                    
                    const edgeLength = Math.sqrt(edge.x * edge.x + edge.y * edge.y);
                    const projection = (toPoint.x * edge.x + toPoint.y * edge.y) / (edgeLength * edgeLength);
                    const clampedProjection = Math.max(0, Math.min(1, projection));
                    
                    const closestPoint = {
                        x: projectedVertices[i].x + edge.x * clampedProjection,
                        y: projectedVertices[i].y + edge.y * clampedProjection
                    };
                    
                    const distance = Math.sqrt(
                        (point.x - closestPoint.x) ** 2 + (point.y - closestPoint.y) ** 2
                    );
                    
                    if (distance < closestDistance) {
                        closestDistance = distance;
                        normal = new Vector3(
                            -(edge.y / edgeLength),
                            (edge.x / edgeLength),
                            0
                        );
                    }
                }
                
                return normal;
            }
        }
        
        // Ball class
        class Ball {
            constructor(x = 0, y = 0, z = 0) {
                this.position = new Vector3(x, y, z);
                this.velocity = new Vector3(
                    (Math.random() - 0.5) * 8,
                    (Math.random() - 0.5) * 8,
                    0
                );
                this.radius = 15;
                this.color = '#ff6b6b';
                this.trail = [];
                this.maxTrailLength = 20;
            }
            
            update(hexagon) {
                if (isPaused) return;
                
                // Update position
                this.position = this.position.add(this.velocity.multiply(speedMultiplier));
                
                // Add to trail
                this.trail.push({ ...this.position });
                if (this.trail.length > this.maxTrailLength) {
                    this.trail.shift();
                }
                
                // Check collision with hexagon boundaries
                const projected = project3D(this.position);
                
                if (!hexagon.isPointInside(projected)) {
                    const normal = hexagon.getCollisionNormal(projected);
                    
                    // Reflect velocity
                    const dot = this.velocity.x * normal.x + this.velocity.y * normal.y;
                    this.velocity = new Vector3(
                        this.velocity.x - 2 * dot * normal.x,
                        this.velocity.y - 2 * dot * normal.y,
                        this.velocity.z
                    );
                    
                    // Move ball back inside
                    while (!hexagon.isPointInside(project3D(this.position))) {
                        this.position = this.position.add(normal.multiply(0.5));
                    }
                }
                
                // Add some damping to prevent infinite acceleration
                this.velocity = this.velocity.multiply(0.999);
            }
            
            draw() {
                // Draw trail
                ctx.strokeStyle = 'rgba(255, 107, 107, 0.3)';
                ctx.lineWidth = 2;
                ctx.beginPath();
                
                this.trail.forEach((point, i) => {
                    const projected = project3D(point);
                    const alpha = i / this.trail.length;
                    
                    if (i === 0) {
                        ctx.moveTo(projected.x, projected.y);
                    } else {
                        ctx.lineTo(projected.x, projected.y);
                    }
                });
                ctx.stroke();
                
                // Draw ball
                const projected = project3D(this.position);
                const scaledRadius = this.radius * projected.scale;
                
                ctx.beginPath();
                ctx.arc(projected.x, projected.y, scaledRadius, 0, Math.PI * 2);
                
                // Create gradient for 3D effect
                const gradient = ctx.createRadialGradient(
                    projected.x - scaledRadius * 0.3,
                    projected.y - scaledRadius * 0.3,
                    0,
                    projected.x,
                    projected.y,
                    scaledRadius
                );
                gradient.addColorStop(0, '#ffaaaa');
                gradient.addColorStop(1, this.color);
                
                ctx.fillStyle = gradient;
                ctx.fill();
                
                ctx.strokeStyle = '#cc5555';
                ctx.lineWidth = 2;
                ctx.stroke();
            }
            
            reset() {
                this.position = new Vector3(0, 0, 0);
                this.velocity = new Vector3(
                    (Math.random() - 0.5) * 8,
                    (Math.random() - 0.5) * 8,
                    0
                );
                this.trail = [];
            }
        }
        
        // Initialize game objects
        const hexagon = new Hexagon(200);
        const ball = new Ball();
        
        // Control functions
        function togglePause() {
            isPaused = !isPaused;
        }
        
        function resetBall() {
            ball.reset();
        }
        
        function changeSpeed() {
            speedMultiplier = speedMultiplier === 1 ? 2 : speedMultiplier === 2 ? 0.5 : 1;
        }
        
        // Game loop
        function gameLoop() {
            // Clear canvas
            ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Update and draw
            hexagon.update();
            ball.update(hexagon);
            
            hexagon.draw();
            ball.draw();
            
            // Draw info
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.fillText(`Speed: ${speedMultiplier}x`, canvas.width - 120, 30);
            ctx.fillText(isPaused ? 'PAUSED' : 'RUNNING', canvas.width - 120, 50);
            
            requestAnimationFrame(gameLoop);
        }
        
        // Start the game
        gameLoop();
    </script>
</body>
</html>
